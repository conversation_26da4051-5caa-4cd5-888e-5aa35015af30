# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

steps:
  - name: "python:3.11-slim" 
    id: install-dependencies
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        pip install uv==0.6.12 --user && uv sync --locked
    env:
      - 'PATH=/usr/local/bin:/usr/bin:~/.local/bin'

  - name: "python:3.11-slim"
    id: trigger-deployment
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        uv export --no-hashes --no-sources --no-header --no-dev --no-emit-project --no-annotate --locked > .requirements.txt
        uv run app/agent_engine_app.py \
          --project ${_PROD_PROJECT_ID} \
          --location ${_REGION} \
          --service-account ${_APP_SA_EMAIL_PROD} \
          --set-env-vars="COMMIT_SHA=${COMMIT_SHA}"
    env:
      - 'PATH=/usr/local/bin:/usr/bin:~/.local/bin'

substitutions:
  _PROD_PROJECT_ID: YOUR_PROD_PROJECT_ID
  _REGION: us-central1

logsBucket: gs://${PROJECT_ID}-my-fullstack-agent-logs-data/build-logs
options:
  substitutionOption: ALLOW_LOOSE
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
