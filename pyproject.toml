[project]
name = "gemini-fullstack"
version = "0.1.0"
description = ""
authors = [
    {name = "Amit Dutta", email = "<EMAIL>"},
    {name = "Deepak Moonat", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "<PERSON><PERSON><PERSON><PERSON>@google.com"},
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
]
dependencies = [
    "google-adk==1.4.2",
    "llama-index-core>=0.10.0",
]

requires-python = ">=3.10,<3.13"

[dependency-groups]
dev = [
    "pytest~=8.3.2",
    "pytest-asyncio~=0.23.7",
    "nest-asyncio>=1.6.0",
]

[project.optional-dependencies]

jupyter = [
    "jupyter~=1.0.0",
]

lint = [
    "ruff>=0.4.6",
    "mypy~=1.15.0",
    "codespell~=2.2.0",
    "types-pyyaml~=6.0.12.20240917",
    "types-requests~=2.32.0.20240914",
]

[tool.ruff]
line-length = 88
target-version = "py310"

[tool.ruff.lint]
select = [
    "E",   # pycodestyle
    "F",   # pyflakes
    "W",   # pycodestyle warnings
    "I",   # isort
    "C",  # flake8-comprehensions
    "B",   # flake8-bugbear
    "UP", # pyupgrade
    "RUF", # ruff specific rules
]
ignore = ["E501", "C901"] # ignore line too long, too complex

[tool.ruff.lint.isort]
known-first-party = ["app", "frontend"]

[tool.mypy]
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
no_implicit_optional = true
check_untyped_defs = true
disallow_subclassing_any = true
warn_incomplete_stub = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unreachable = true
follow_imports = "silent"
ignore_missing_imports = true
explicit_package_bases = true
disable_error_code = ["misc", "no-untyped-call", "no-any-return"]

exclude = [".venv"]

[tool.codespell]
ignore-words-list = "rouge"
skip = "./locust_env/*,uv.lock,.venv,./frontend,**/*.ipynb"


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.pytest.ini_options]
pythonpath = "."
asyncio_default_fixture_loop_scope = "function"

[tool.hatch.build.targets.wheel]
packages = ["app","frontend"]

# This configuration file is used by goo.gle/agent-starter-pack to power remote templating.
# It defines the template's properties, settings, and deployment options.
[tool.agent-starter-pack]
base_template = "adk_base"
description = "A production-ready fullstack research agent that uses Gemini to strategize, research, and synthesize comprehensive reports with human-in-the-loop collaboration"
example_question = "A report on the latest Google I/O event"

[tool.agent-starter-pack.settings]
deployment_targets = ["agent_engine", "cloud_run"]
interactive_command = "dev"
