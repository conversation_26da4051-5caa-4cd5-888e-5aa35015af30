# RAG Agent Setup Guide

## Overview

The research agent has been successfully transformed into a RAG (Retrieval-Augmented Generation) agent. This document outlines the required environment variables and setup steps.

## Required Environment Variables

### 1. RAG_CORPUS (Required)
The RAG corpus resource name from Vertex AI RAG.

**Format**: `projects/{PROJECT_ID}/locations/{LOCATION}/ragCorpora/{CORPUS_ID}`

**Example**: `projects/123456789/locations/us-central1/ragCorpora/456789123`

**How to obtain**:
1. Create a RAG corpus using the provided script: `python rag/shared_libraries/prepare_corpus_and_data.py`
2. The script will automatically set this variable in your `.env` file

### 2. Google Cloud Configuration (Required)
These are the same as the original research agent:

```bash
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1  # or your preferred region
GOOGLE_GENAI_USE_VERTEXAI=True
```

### 3. Alternative: AI Studio Configuration
For development with AI Studio instead of Vertex AI:

```bash
GOOGLE_GENAI_USE_VERTEXAI=False
GOOGLE_API_KEY=your-ai-studio-api-key
```

## Setup Steps

### 1. Create Environment File
Create a `.env` file in the project root:

```bash
# For Vertex AI (recommended for production)
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_USE_VERTEXAI=True
RAG_CORPUS=projects/your-project-id/locations/us-central1/ragCorpora/your-corpus-id

# OR for AI Studio (development only)
GOOGLE_GENAI_USE_VERTEXAI=False
GOOGLE_API_KEY=your-ai-studio-api-key
RAG_CORPUS=projects/your-project-id/locations/us-central1/ragCorpora/your-corpus-id
```

### 2. Create RAG Corpus
Use the provided script to create a RAG corpus with sample data:

```bash
cd rag/shared_libraries
python prepare_corpus_and_data.py
```

This script will:
- Create a new RAG corpus in Vertex AI
- Upload sample documents (Alphabet 10-K 2024)
- Automatically update your `.env` file with the corpus ID

### 3. Install Dependencies
The transformation has added the required `llama-index-core` dependency:

```bash
uv sync
```

### 4. Run the Application
Start the development servers:

```bash
make dev
```

Or separately:
```bash
make dev-backend  # Backend on http://localhost:8000
make dev-frontend # Frontend on http://localhost:5173
```

## Key Changes Made

### 1. Agent Architecture
- **Before**: Complex multi-agent pipeline with web search
- **After**: Simple single agent with RAG retrieval

### 2. Configuration
- Updated `app/config.py` to support RAG-specific settings
- Added `RAG_CORPUS` environment variable support

### 3. Dependencies
- Added `llama-index-core>=0.10.0` to `pyproject.toml`

### 4. Agent Logic
- Replaced complex research pipeline with simple RAG agent
- Maintained same interface for frontend compatibility
- Preserved deployment infrastructure

## Frontend Compatibility

The frontend remains completely unchanged and functional:
- Same Streamlit UI
- Same React components
- Same API endpoints
- Same deployment process

## Deployment

The RAG agent can be deployed using the same methods as the original:

### Agent Engine
```bash
make backend
```

### Cloud Run
```bash
adk deploy cloud_run
```

## Troubleshooting

### Error: "Request contains an invalid argument"
This typically means the `RAG_CORPUS` environment variable is not set or invalid.

**Solution**: 
1. Ensure `RAG_CORPUS` is set in your `.env` file
2. Verify the corpus exists in Vertex AI
3. Check that your Google Cloud credentials have access to the corpus

### Error: "No module named 'llama_index'"
The required dependency is missing.

**Solution**: Run `uv sync` to install dependencies

## Next Steps

1. Set up your `.env` file with the required variables
2. Run the corpus creation script
3. Test the RAG agent with your own documents
4. Deploy to your preferred platform

The RAG agent is now ready to use with the same frontend experience as the original research agent!
